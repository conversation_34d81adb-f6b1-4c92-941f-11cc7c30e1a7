import { PageQueryParam, PageResponse } from '@/types/common';
import { Moment } from 'moment';

/**
 * 巡检异常记录信息 VO
 */
export interface ExceptionRecordsVo {
  /** 主键 */
  id?: string;
  /** 任务id */
  taskId?: string;
  /** 任务检查点id */
  taskPointId?: string;
  /** 异常类型 */
  exceptionType?: string;
  /** 异常发生时间 */
  exceptionTime?: string | Date | Moment;
  /** 异常描述 */
  exceptionDescribe?: string;
  /** 上报人id */
  reportUserId?: string;
  /** 上报人姓名 */
  reportUserName?: string;
  /** 处置状态 */
  handleStatus?: number;
  /** 处置人 */
  handleUserId?: string;
  /** 处置人姓名 */
  handleUserName?: string;
  /** 处置方式 */
  handleMethod?: string;
  /** 处置时间 */
  handleTime?: string | Date | Moment;
  /** 处置附件 */
  handleFiles?: string;
  /** 创建时间 */
  createDate?: string | Date | Moment;
  /** 更新时间 */
  updateDate?: string | Date | Moment;
}
/**
 * 异常记录 查询参数
 */
export interface ExceptionRecordsQueryParam {
  taskId?: string;
  exceptionType?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  handleStatus?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  startDate?: string;
  endDate?: string;
  startDate_end?: string;
  startDate_start?: string;
}

/**
 * @description 新增参数
 * @param data 新增参数
 */
export interface ExceptionRecordsInsertParam extends ExceptionRecordsVo {
  id?: string;
}

/**
 * @description 更新参数
 * @param data 更新参数
 */
export interface ExceptionRecordsUpdateParam extends ExceptionRecordsVo {
  id?: string;
}
/**
 * @description 分页返回结果
 * @param data 分页查询参数
 */
export interface ExceptionRecordsPageResponse extends PageResponse<ExceptionRecordsVo> {
  data: ExceptionRecordsVo[];
}

/**
 * @description 分页查询参数
 * @param data 分页查询参数
 */
export interface ExceptionRecordsPageQueryParam extends PageQueryParam<ExceptionRecordsQueryParam> {
  condition: ExceptionRecordsQueryParam;
}
