import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization, YTHDialog } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, message, Space } from 'antd';
import locales from '@/locales';
import PlanApi from '@/service/inspection/planApi';
import {
  InspectionPlanVo,
  PlanPageResponse,
  PlanQueryParam,
  PlanQueryParamFilter,
} from '@/types/inspection/plan';
import baseApi from '@/service/baseApi';
import dicParams from './dicParams';
import InspectionPlanModal from './components/InspectionPlanModal';

/**
 * @description 巡检计划
 */
const InspectionPlanList: React.FC = () => {
  const listActionRef: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<string>(''); // 是否是新增模式
  const [dataObj, setDataObj] = useState<{ [key: string]: React.Key }>({}); // 查看或编辑行数据
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  const columns: IYTHColumnProps[] = [
    { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
    {
      dataIndex: 'planCode',
      title: '计划编码',
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'planName',
      title: '计划名称',
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'inspectionMethod',
      title: '巡检方式',
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return baseApi.getDictionary(dicParams.INSPECTION_METHOD);
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (_, record) => {
        return record.inspectionMethodText || '-';
      },
    },
    {
      dataIndex: 'planType',
      title: '计划类型',
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return baseApi.getDictionary(dicParams.PLAN_TYPE);
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (_, record) => {
        return record.planTypeText || '-';
      },
    },
    {
      dataIndex: 'frequencyNumber',
      title: '周期',
      query: false,
      display: true,
    },
    {
      dataIndex: 'executionFrequency',
      title: '单位',
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return baseApi.getDictionary(dicParams.EXECUTION_FREQUENCY);
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (_, record) => {
        return record.executionFrequencyText || '-';
      },
    },
    {
      dataIndex: 'taskDate',
      title: '初次巡检时间',
      query: false,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'isUsed',
      title: '计划状态',
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: () => {
          return [
            { code: '1', text: '启用' },
            { code: '0', text: '停用' },
          ];
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (val: string) => {
        return String(val) === '1' ? '启用' : '停用';
      },
    },
    {
      dataIndex: 'isRemind',
      title: '是否短信提醒',
      query: false,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: () => {
          return [
            { code: '1', text: '是' },
            { code: '0', text: '否' },
          ];
        },
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (val: string) => {
        return val === '1' ? '是' : '否';
      },
    },
    {
      dataIndex: 'directorUserName',
      title: '负责人',
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
    },
    {
      dataIndex: 'createDate',
      title: '创建时间',
      width: 120,
      query: false,
      display: true,
    },
  ];
  // 设置弹窗标题
  const setModalTitle: () => string = () => {
    let title: string = '';
    if (modalType === 'add') {
      title = '新增';
    } else if (modalType === 'view') {
      title = '查看';
    } else if (modalType === 'edit') {
      title = '编辑';
    }
    return title;
  };

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    listAction.reload({});
    Modal.destroyAll();
  };

  /** 删除方法 */
  const confirmDelete: (row: InspectionPlanVo) => Promise<void> = async (row) => {
    const res: { code?: number; msg?: string } = await PlanApi.deleteById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      listAction.reload({});
    } else {
      message.error('删除数据出错');
    }
  };

  /** 删除确认弹窗 */
  const deleteTemplateDialog: (row: InspectionPlanVo) => Promise<void> = async (row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  /** 处理查询参数：从选择器数组中提取第一个选项的 code 值 */
  const handleFilter: (filter: PlanQueryParamFilter) => PlanQueryParam = (
    filter: PlanQueryParamFilter,
  ): PlanQueryParam => {
    return {
      // Input 组件直接取值
      planCode: filter.planCode,
      planName: filter.planName,
      directorUserName: filter.directorUserName,
      // Selector 组件从选中数组的第一个对象中提取 code
      inspectionMethod: filter.inspectionMethod?.[0]?.code,
      planType: filter.planType?.[0]?.code,
      executionFrequency: filter.executionFrequency?.[0]?.code,
      isUsed: filter.isUsed?.[0]?.code,
    };
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="InspectionPlanList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        extraOperation={[]}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        request={async (filter, pagination) => {
          try {
            const resData: PlanPageResponse = await PlanApi.queryByPage({
              aescs: [],
              descs: [],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });
            if (resData.code && resData.code === 200) {
              resData.data.forEach((item, index) => {
                resData.data[index].serialNo =
                  (pagination.current - 1) * pagination.pageSize + index + 1;
              });
              return {
                data: resData.data,
                total: resData.total,
                success: true,
              };
            }
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        rowOperationWidth={200}
        rowOperation={(row) => {
          return [
            {
              element: (
                <Space size="middle">
                  <Button
                    size="small"
                    type="link"
                    onClick={() => {
                      setModalType('view');
                      setDataObj(row);
                      setEditMenuVisiable(true);
                    }}
                  >
                    查看
                  </Button>

                  <Button
                    size="small"
                    type="link"
                    disabled={row.inspectionMethod === dicParams.INSPECTION_METHOD_UAV}
                    onClick={() => {
                      setModalType('edit');
                      setDataObj(row);
                      setEditMenuVisiable(true);
                    }}
                  >
                    编辑
                  </Button>

                  <Button
                    size="small"
                    type="link"
                    danger
                    disabled={row.lastCreateTaskDate}
                    onClick={() => {
                      deleteTemplateDialog(row as InspectionPlanVo);
                    }}
                  >
                    删除
                  </Button>
                </Space>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="60%"
        title={setModalTitle()}
        footer={null}
        visible={editMenuVisiable}
        onCancel={closeModal}
        destroyOnClose
        maskClosable={false}
      >
        <InspectionPlanModal type={modalType} closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  InspectionPlanList,
  locales['zh-CN'],
  YTHLocalization.getLanguage(),
);
